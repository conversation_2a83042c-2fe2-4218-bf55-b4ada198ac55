# College Images Setup Guide

## 📁 Directory Structure Setup

Please follow these steps to organize your college images:

### Step 1: Create Images Directory
1. Create a folder called `images` inside the `public` directory
2. Inside `public/images`, create a subfolder called `colleges`
3. Your structure should look like: `public/images/colleges/`

### Step 2: Image Naming Convention
Your images should be named using the college acronyms for consistency. Here's the mapping:

## 🏫 College Image Mapping (50 Colleges)

| ID | College Name | Acronym | Image Filename |
|----|--------------|---------|----------------|
| 1 | Rashtreeya Vidyalaya College of Engineering | RVCE | rvce.jpg |
| 2 | RV Institute of Technology and Management | RVITM | rvitm.jpg |
| 3 | RV University | RVU | rvu.jpg |
| 4 | PES University (Ring Road Campus) | PESURRC | pesurrc.jpg |
| 5 | BMS College of Engineering | BMSCE | bmsce.jpg |
| 6 | MS Ramaiah Institute of Technology | MSRIT | msrit.jpg |
| 7 | Sir <PERSON> Visvesvaraya Institute of Technology | Sir MVIT | sir-mvit.jpg |
| 8 | Bangalore Institute of Technology | BIT | bit.jpg |
| 9 | Nitte Meenakshi Institute of Technology | NMIT | nmit.jpg |
| 10 | PES University (Electronic City Campus) | PESUECC | pesuecc.jpg |
| 11 | CMR Institute of Technology | CMRIT | cmrit.jpg |
| 12 | Dayananda Sagar College of Engineering | DSCE | dsce.jpg |
| 13 | BMS Institute of Technology | BMSIT | bmsit.jpg |
| 14 | Reva University | REVA | reva.jpg |
| 15 | MS Ramaiah University of Applied Sciences | MSRUAS | msruas.jpg |
| 16 | R.N. Shetty Institute of Technology | RNSIT | rnsit.jpg |
| 17 | JSS Academy of Technical Education | JSSATE | jssate.jpg |
| 18 | National Institute of Engineering | NIE | nie.jpg |
| 19 | New Horizon College of Engineering | NHCE | nhce.jpg |
| 20 | CMR University | CMRU | cmru.jpg |
| 21 | BNM Institute of Technology | BNMIT | bnmit.jpg |
| 22 | Dr. B R Ambedkar Institute of Technology | Dr. B R AIT | dr-br-ait.jpg |
| 23 | Sapthagiri College of Engineering | SCE | sce.jpg |
| 24 | Atria Institute of Technology | AIT | ait.jpg |
| 25 | Presidency University | PU | pu.jpg |
| 26 | Acharya Institute of Technology | Acharya IT | acharya-it.jpg |
| 27 | Global Academy of Technology | GAT | gat.jpg |
| 28 | Vidyavardhaka College of Engineering | VVCE | vvce.jpg |
| 29 | KNS Institute of Technology | KNSIT | knsit.jpg |
| 30 | East West Institute of Technology | EWIT | ewit.jpg |
| 31 | Siddaganga Institute of Technology | SIT | sit.jpg |
| 32 | Malnad College of Engineering | MCE | mce.jpg |
| 33 | Basaveshwar Engineering College | BEC | bec.jpg |
| 34 | KLE Technological University | KLETU | kletu.jpg |
| 35 | SDM College of Engineering and Technology | SDMCET | sdmcet.jpg |
| 36 | Manipal Institute of Technology | MIT | mit.jpg |
| 37 | NMAM Institute of Technology | NMAMIT | nmamit.jpg |
| 38 | Sahyadri College of Engineering and Management | SCEM | scem.jpg |
| 39 | Canara Engineering College | CEC | cec.jpg |
| 40 | Bearys Institute of Technology | BIT Mangalore | bit-mangalore.jpg |
| 41 | Srinivas Institute of Technology | SIT Mangalore | sit-mangalore.jpg |
| 42 | Shree Devi Institute of Technology | SDIT | sdit.jpg |
| 43 | Alva's Institute of Engineering and Technology | AIET | aiet.jpg |
| 44 | St. Joseph Engineering College | SJEC | sjec.jpg |
| 45 | Yenepoya Institute of Technology | YIT | yit.jpg |
| 46 | Bapuji Institute of Engineering and Technology | BIET | biet.jpg |
| 47 | Ballari Institute of Technology and Management | BITM | bitm.jpg |
| 48 | Cambridge Institute of Technology | CIT | cit.jpg |
| 49 | T John Institute of Technology | TJIT | tjit.jpg |
| 50 | RR Institute of Technology | RRIT | rrit.jpg |

## 📋 Instructions for You:

### Step 1: Organize Your Images
1. Rename your college images according to the filenames above (e.g., rvce.jpg, msrit.jpg, etc.)
2. Make sure all images are in a web-friendly format (JPG, PNG, or WebP)
3. Recommended image dimensions: 800x600 pixels or similar aspect ratio
4. Keep file sizes reasonable (under 500KB each for better performance)

### Step 2: Copy Images to Project
1. Copy all renamed images to: `public/images/colleges/`
2. Your final structure should be:
   ```
   public/
   ├── images/
   │   └── colleges/
   │       ├── rvce.jpg
   │       ├── rvitm.jpg
   │       ├── rvu.jpg
   │       ├── pesurrc.jpg
   │       └── ... (all 50 images)
   ```

### Step 3: Let Me Know When Ready
Once you've organized the images as described above, let me know and I'll:
1. Update the college.json file with the correct image paths
2. Update the Next.js configuration for image optimization
3. Test that all images are loading correctly

## 🔧 Technical Notes:
- Images will be automatically optimized by Next.js
- We'll use responsive images for better performance
- Alt text will be generated automatically from college names
- Images will be lazy-loaded for better page speed

## ❓ Need Help?
If you have any questions about organizing the images or need help with specific college name mappings, just let me know!
