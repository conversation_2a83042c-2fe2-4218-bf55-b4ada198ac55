// Hook for detecting exit intent
import { useState, useEffect } from 'react';

export function useExitIntent() {
  const [showExitIntent, setShowExitIntent] = useState(false);
  const [hasShownExitIntent, setHasShownExitIntent] = useState(false);

  useEffect(() => {
    // Check if exit intent has already been shown in this session
    const exitIntentShown = sessionStorage.getItem('exitIntentShown');
    if (exitIntentShown) {
      setHasShownExitIntent(true);
      return;
    }

    let isExiting = false;

    const handleMouseLeave = (e) => {
      // Only trigger if mouse is leaving from the top of the page
      if (e.clientY <= 0 && !hasShownExitIntent && !isExiting) {
        isExiting = true;
        setShowExitIntent(true);
        setHasShownExitIntent(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    };

    const handleMouseEnter = () => {
      isExiting = false;
    };

    // Add event listeners
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseenter', handleMouseEnter);

    // Cleanup
    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseenter', handleMouseEnter);
    };
  }, [hasShownExitIntent]);

  const hideExitIntent = () => {
    setShowExitIntent(false);
  };

  return { showExitIntent, hideExitIntent };
}
