// Chatbot utilities for natural language processing and college data queries
import { getAllColleges } from './collegeData';

// Intent patterns for different types of queries
const INTENT_PATTERNS = {
  COURSE_SEARCH: [
    /(?:show|find|list|which|what).*(?:colleges?|universities?).*(?:offering|with|have|has).*(?:ai|ml|artificial intelligence|machine learning|computer science|cse|ece|mechanical|civil|electrical)/i,
    /(?:colleges?|universities?).*(?:for|offering|with).*(?:ai|ml|artificial intelligence|machine learning|computer science|cse|ece|mechanical|civil|electrical)/i,
    /(?:ai|ml|artificial intelligence|machine learning|computer science|cse|ece|mechanical|civil|electrical).*(?:colleges?|universities?)/i
  ],
  PLACEMENT_QUERY: [
    /(?:highest|best|top).*(?:placement|package|salary)/i,
    /(?:placement|package|salary).*(?:highest|best|top)/i,
    /(?:which|what).*(?:college|university).*(?:highest|best|top).*(?:placement|package|salary)/i,
    /(?:90|95|100).*(?:percent|%).*(?:placement)/i,
    /(?:placement).*(?:rate|percentage)/i
  ],
  COLLEGE_COMPARISON: [
    /(?:compare|comparison|vs|versus).*(?:college|university)/i,
    /(?:difference|diff).*(?:between)/i,
    /(?:rvce|pesu|bmsce|msrit).*(?:vs|versus|compared to)/i
  ],
  LOCATION_SEARCH: [
    /(?:colleges?|universities?).*(?:in|near|around).*(?:bangalore|bengaluru|karnataka)/i,
    /(?:bangalore|bengaluru).*(?:colleges?|universities?)/i,
    /(?:engineering).*(?:colleges?).*(?:bangalore|bengaluru)/i
  ],
  FEE_QUERY: [
    /(?:fee|fees|cost|price).*(?:under|below|less than)/i,
    /(?:cheap|affordable|low cost).*(?:colleges?|universities?)/i,
    /(?:which|what).*(?:college|university).*(?:cheapest|lowest fee)/i
  ],
  GENERAL_INFO: [
    /(?:tell me about|information about|details about)/i,
    /(?:what is|who is|where is)/i,
    /(?:help|guide|guidance)/i
  ]
};

// Synonyms for better matching
const SYNONYMS = {
  'best': ['top', 'highest', 'excellent', 'premier', 'leading'],
  'college': ['university', 'institution', 'institute'],
  'computer science': ['cse', 'cs', 'computer engineering'],
  'artificial intelligence': ['ai', 'machine learning', 'ml', 'ai/ml'],
  'electronics': ['ece', 'electronics and communication'],
  'mechanical': ['mech', 'mechanical engineering'],
  'civil': ['civil engineering'],
  'electrical': ['eee', 'electrical and electronics'],
  'placement': ['job', 'recruitment', 'hiring', 'career'],
  'package': ['salary', 'ctc', 'compensation', 'pay'],
  'bangalore': ['bengaluru', 'blr']
};

// Extract intent from user message
export function extractIntent(message) {
  const lowerMessage = message.toLowerCase();
  
  for (const [intent, patterns] of Object.entries(INTENT_PATTERNS)) {
    for (const pattern of patterns) {
      if (pattern.test(lowerMessage)) {
        return intent;
      }
    }
  }
  
  return 'GENERAL_INFO';
}

// Extract entities (colleges, courses, etc.) from message
export function extractEntities(message) {
  const entities = {
    colleges: [],
    courses: [],
    locations: [],
    numbers: []
  };
  
  const lowerMessage = message.toLowerCase();
  
  // Extract college names and acronyms
  const collegePatterns = [
    'rvce', 'rashtreeya vidyalaya', 'rv college',
    'pesu', 'pes university', 'pes',
    'bmsce', 'bms college', 'bms',
    'msrit', 'ms ramaiah', 'ramaiah',
    'dsce', 'dayananda sagar',
    'nmit', 'nitte meenakshi',
    'cmrit', 'cmr institute',
    'bit', 'bangalore institute'
  ];
  
  collegePatterns.forEach(pattern => {
    if (lowerMessage.includes(pattern)) {
      entities.colleges.push(pattern);
    }
  });
  
  // Extract course names
  const coursePatterns = [
    'computer science', 'cse', 'cs',
    'artificial intelligence', 'ai', 'machine learning', 'ml',
    'electronics', 'ece',
    'mechanical', 'mech',
    'civil',
    'electrical', 'eee',
    'biotechnology', 'biotech',
    'information science', 'ise'
  ];
  
  coursePatterns.forEach(pattern => {
    if (lowerMessage.includes(pattern)) {
      entities.courses.push(pattern);
    }
  });
  
  // Extract numbers (for fees, packages, etc.)
  const numberMatches = message.match(/\d+(?:\.\d+)?(?:\s*(?:lakh|crore|k|lakhs|crores))?/gi);
  if (numberMatches) {
    entities.numbers = numberMatches;
  }
  
  return entities;
}

// Search colleges based on intent and entities
export async function searchColleges(intent, entities, message) {
  const colleges = getAllColleges();
  let results = [...colleges];
  
  switch (intent) {
    case 'COURSE_SEARCH':
      results = filterByCourse(results, entities.courses);
      break;
      
    case 'PLACEMENT_QUERY':
      results = filterByPlacement(results, entities.numbers);
      break;
      
    case 'LOCATION_SEARCH':
      // All colleges are in Bangalore, so return all
      break;
      
    case 'FEE_QUERY':
      results = filterByFees(results, entities.numbers);
      break;
      
    case 'COLLEGE_COMPARISON':
      results = filterForComparison(results, entities.colleges);
      break;
      
    default:
      // Return top colleges for general queries
      results = results.slice(0, 5);
  }
  
  return results;
}

// Filter colleges by course offerings
function filterByCourse(colleges, courses) {
  if (courses.length === 0) return colleges.slice(0, 10);
  
  return colleges.filter(college => {
    const coursesText = college.coursesOffered.toLowerCase();
    return courses.some(course => {
      const normalizedCourse = normalizeCourse(course);
      return coursesText.includes(normalizedCourse) || 
             coursesText.includes(course.toLowerCase());
    });
  }).slice(0, 10);
}

// Filter colleges by placement criteria
function filterByPlacement(colleges, numbers) {
  if (numbers.length === 0) {
    // Return colleges sorted by placement rate
    return colleges
      .sort((a, b) => b.placementRate - a.placementRate)
      .slice(0, 10);
  }
  
  const targetRate = parseFloat(numbers[0]);
  return colleges
    .filter(college => college.placementRate >= targetRate)
    .sort((a, b) => b.placementRate - a.placementRate)
    .slice(0, 10);
}

// Filter colleges by fees (placeholder - fees not in current data)
function filterByFees(colleges, numbers) {
  // Since fee data is not available, return colleges sorted by ranking
  return colleges.slice(0, 10);
}

// Filter colleges for comparison
function filterForComparison(colleges, collegeNames) {
  if (collegeNames.length === 0) return colleges.slice(0, 3);
  
  const matchedColleges = colleges.filter(college => {
    const collegeName = college.name.toLowerCase();
    const acronym = college.acronym.toLowerCase();
    
    return collegeNames.some(name => 
      collegeName.includes(name) || 
      acronym.includes(name) ||
      name.includes(acronym)
    );
  });
  
  return matchedColleges.length > 0 ? matchedColleges : colleges.slice(0, 3);
}

// Normalize course names for better matching
function normalizeCourse(course) {
  const courseMap = {
    'ai': 'artificial intelligence',
    'ml': 'machine learning',
    'cse': 'computer science',
    'cs': 'computer science',
    'ece': 'electronics and communication',
    'eee': 'electrical and electronics',
    'mech': 'mechanical',
    'ise': 'information science'
  };
  
  return courseMap[course.toLowerCase()] || course.toLowerCase();
}

// Generate response based on search results
export function generateResponse(intent, results, originalMessage) {
  if (results.length === 0) {
    return {
      text: "I couldn't find specific colleges matching your criteria. Let me connect you with our education counselors who can provide personalized guidance!",
      showContactForm: true,
      colleges: []
    };
  }
  
  let responseText = '';
  
  switch (intent) {
    case 'COURSE_SEARCH':
      responseText = `Here are the top colleges offering the courses you're looking for:\n\n`;
      break;
      
    case 'PLACEMENT_QUERY':
      responseText = `Here are colleges with excellent placement records:\n\n`;
      break;
      
    case 'COLLEGE_COMPARISON':
      responseText = `Here's a comparison of the colleges:\n\n`;
      break;
      
    case 'LOCATION_SEARCH':
      responseText = `Here are the top engineering colleges in Bangalore:\n\n`;
      break;
      
    default:
      responseText = `Here are some top engineering colleges that might interest you:\n\n`;
  }
  
  // Add college information to response
  results.slice(0, 3).forEach((college, index) => {
    responseText += `**${index + 1}. ${college.name} (${college.acronym})**\n`;
    responseText += `📍 Ranking: #${college.ranking}\n`;
    responseText += `📊 Placement Rate: ${college.placementRate}%\n`;
    responseText += `💰 Highest Package: ₹${college.highestPackage} LPA\n`;
    responseText += `🏛️ Campus: ${college.campusSize}\n\n`;
  });
  
  if (results.length > 3) {
    responseText += `And ${results.length - 3} more colleges match your criteria!\n\n`;
  }
  
  responseText += `Would you like more detailed information about any of these colleges or need help with admissions guidance?`;
  
  return {
    text: responseText,
    showContactForm: false,
    colleges: results.slice(0, 5)
  };
}
