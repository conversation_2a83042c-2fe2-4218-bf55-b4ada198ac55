'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ArrowRight, 
  Award, 
  Users, 
  TrendingUp, 
  MapPin, 
  CheckCircle, 
  Star,
  BookOpen,
  Target,
  Shield
} from 'lucide-react';
import CollegeCard from '../components/CollegeCard';
import { getFeaturedColleges, getAggregateStats, getTopCompanies } from '../lib/collegeData';

export default function HomePage() {
  const [featuredColleges, setFeaturedColleges] = useState([]);
  const [stats, setStats] = useState({});
  const [topCompanies, setTopCompanies] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    setFeaturedColleges(getFeaturedColleges());
    setStats(getAggregateStats());
    setTopCompanies(getTopCompanies());
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % Math.max(1, featuredColleges.length - 2));
    }, 5000);
    return () => clearInterval(timer);
  }, [featuredColleges.length]);

  const trustIndicators = [
    { icon: Users, label: '1000+ Students Guided', color: 'text-blue-600' },
    { icon: TrendingUp, label: 'Real-Time Placement Data', color: 'text-green-600' },
    { icon: Target, label: 'AI-Powered College Matcher', color: 'text-purple-600' },
    { icon: Shield, label: 'Verified Information', color: 'text-orange-600' },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="gradient-bg text-white section-padding">
        <div className="container-max">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-slide-up">
              Your Gateway to Top Engineering Colleges in{' '}
              <span className="text-yellow-300">Bangalore</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 animate-slide-up">
              Compare placements, courses, and rankings. Let us guide you to the perfect fit!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
              <a
                href="https://wa.me/************?text=Hi! I need personalized guidance for engineering colleges in Bangalore."
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-xl flex items-center space-x-2"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                <span>Get Personalized Guidance (Free WhatsApp Consultation)</span>
              </a>
              <Link
                href="/colleges"
                className="bg-white hover:bg-gray-100 text-primary-600 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-xl flex items-center space-x-2"
              >
                <BookOpen className="w-6 h-6" />
                <span>Explore All Colleges</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Key Performance Metrics */}
      <section className="bg-white section-padding">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Platform?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get access to comprehensive data and insights about Bangalore's top engineering colleges
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <div className="text-center p-6 card card-hover min-h-[180px] flex flex-col justify-center">
              <div className="bg-green-100 text-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">{stats.avgPlacementRate}%</div>
              <div className="text-gray-600 text-sm leading-tight">Average Placement Rate</div>
            </div>

            <div className="text-center p-6 card card-hover min-h-[180px] flex flex-col justify-center">
              <div className="bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">₹1.15 Cr</div>
              <div className="text-gray-600 text-sm leading-tight">Highest Package</div>
            </div>

            <div className="text-center p-6 card card-hover min-h-[180px] flex flex-col justify-center">
              <div className="bg-purple-100 text-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">{stats.avgCampusSize}</div>
              <div className="text-gray-600 text-sm leading-tight">Average Campus Size (Acres)</div>
            </div>

            <div className="text-center p-6 card card-hover min-h-[180px] flex flex-col justify-center">
              <div className="bg-orange-100 text-orange-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">{stats.totalColleges}+</div>
              <div className="text-gray-600 text-sm leading-tight">Engineering Colleges</div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {trustIndicators.map((indicator, index) => {
              const Icon = indicator.icon;
              return (
                <div key={index} className="flex items-center justify-start space-x-3 p-4 bg-gray-50 rounded-lg min-h-[80px]">
                  <div className="flex-shrink-0">
                    <Icon className={`w-8 h-8 ${indicator.color}`} />
                  </div>
                  <span className="font-semibold text-gray-800 text-sm leading-tight">{indicator.label}</span>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Colleges Carousel */}
      <section className="bg-gray-50 section-padding">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Top Engineering Colleges in Bangalore
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the best engineering colleges with excellent placement records and world-class facilities
            </p>
          </div>

          {featuredColleges.length > 0 && (
            <div className="relative">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {featuredColleges.slice(0, 6).map((college) => (
                  <CollegeCard key={college.id} college={college} />
                ))}
              </div>

              <div className="text-center mt-12">
                <Link
                  href="/colleges"
                  className="btn-primary inline-flex items-center space-x-2"
                >
                  <span>View All Colleges</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Top Companies Section */}
      <section className="bg-white section-padding">
        <div className="container-max">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Top Recruiting Companies
            </h2>
            <p className="text-xl text-gray-600">
              Leading companies that regularly recruit from Bangalore engineering colleges
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {topCompanies.slice(0, 10).map((company, index) => (
              <div key={index} className="text-center p-4 card card-hover">
                <div className="font-semibold text-gray-900 mb-2">{company.company}</div>
                <div className="text-sm text-gray-600">{company.mentions} colleges</div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
