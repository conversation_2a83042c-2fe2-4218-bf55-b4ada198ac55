"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _components_CollegeCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/CollegeCard */ \"(app-pages-browser)/./src/components/CollegeCard.js\");\n/* harmony import */ var _lib_collegeData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/collegeData */ \"(app-pages-browser)/./src/lib/collegeData.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [featuredColleges, setFeaturedColleges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [topCompanies, setTopCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFeaturedColleges((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_4__.getFeaturedColleges)());\n        setStats((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_4__.getAggregateStats)());\n        setTopCompanies((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_4__.getTopCompanies)());\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentSlide((prev)=>(prev + 1) % Math.max(1, featuredColleges.length - 2));\n        }, 5000);\n        return ()=>clearInterval(timer);\n    }, [\n        featuredColleges.length\n    ]);\n    const trustIndicators = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"1000+ Students Guided\",\n            color: \"text-blue-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Real-Time Placement Data\",\n            color: \"text-green-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: \"AI-Powered College Matcher\",\n            color: \"text-purple-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Verified Information\",\n            color: \"text-orange-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"gradient-bg text-white section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6 animate-slide-up\",\n                                children: [\n                                    \"Your Gateway to Top Engineering Colleges in\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-300\",\n                                        children: \"Bangalore\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl mb-8 text-blue-100 animate-slide-up\",\n                                children: \"Compare placements, courses, and rankings. Let us guide you to the perfect fit!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://wa.me/************?text=Hi! I need personalized guidance for engineering colleges in Bangalore.\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-xl flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Get Personalized Guidance (Free WhatsApp Consultation)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/colleges\",\n                                        className: \"bg-white hover:bg-gray-100 text-primary-600 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-xl flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Explore All Colleges\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Our Platform?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Get access to comprehensive data and insights about Bangalore's top engineering colleges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 text-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: [\n                                                stats.avgPlacementRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Average Placement Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: \"₹1.15 Cr\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Highest Package\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-100 text-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: stats.avgCampusSize\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Average Campus Size (Acres)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-100 text-orange-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: [\n                                                stats.totalColleges,\n                                                \"+\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Engineering Colleges\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: trustIndicators.map((indicator, index)=>{\n                                const Icon = indicator.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-start space-x-3 p-4 bg-gray-50 rounded-lg min-h-[80px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-8 h-8 \".concat(indicator.color)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-800 text-sm leading-tight\",\n                                            children: indicator.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gray-50 section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Top Engineering Colleges in Bangalore\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Discover the best engineering colleges with excellent placement records and world-class facilities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        featuredColleges.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: featuredColleges.slice(0, 6).map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CollegeCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            college: college\n                                        }, college.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/colleges\",\n                                        className: \"btn-primary inline-flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View All Colleges\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Top Recruiting Companies\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"Leading companies that regularly recruit from Bangalore engineering colleges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\",\n                            children: topCompanies.slice(0, 10).map((company, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: company.company\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                company.mentions,\n                                                \" colleges\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"Jsd8drd6+zYRFLxwGNU3C6Q8jhY=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});