'use client';

import { useState } from 'react';
import ChatBot from './ChatBot';
import ChatButton from './ChatButton';
import ExitIntentPopup from './ExitIntentPopup';
import { useExitIntent } from '../hooks/useExitIntent';

const ChatbotProvider = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [initialMessage, setInitialMessage] = useState(null);
  const { showExitIntent, hideExitIntent } = useExitIntent();

  const handleOpenChat = (message = null) => {
    setInitialMessage(message);
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
    setInitialMessage(null);
  };

  const handleToggleChat = () => {
    if (isChatOpen) {
      handleCloseChat();
    } else {
      handleOpenChat();
    }
  };

  return (
    <>
      {/* Exit Intent Popup */}
      <ExitIntentPopup
        isVisible={showExitIntent && !isChatOpen}
        onClose={hideExitIntent}
        onStartChat={handleOpenChat}
      />

      {/* Chat Button */}
      <ChatButton
        onClick={handleToggleChat}
        isOpen={isChatOpen}
      />

      {/* Chat Bot */}
      <ChatBot
        isOpen={isChatOpen}
        onClose={handleCloseChat}
        initialMessage={initialMessage}
      />
    </>
  );
};

export default ChatbotProvider;
