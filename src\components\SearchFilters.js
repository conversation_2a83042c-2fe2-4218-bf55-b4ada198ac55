'use client';

import { useState } from 'react';
import { Search, Filter, X, SlidersHorizontal } from 'lucide-react';

export default function SearchFilters({ 
  onSearch, 
  onFilter, 
  onSort, 
  searchQuery = '', 
  filters = {},
  sortBy = 'ranking' 
}) {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [localFilters, setLocalFilters] = useState(filters);

  const sortOptions = [
    { value: 'ranking', label: 'Ranking' },
    { value: 'placementRate', label: 'Placement Rate' },
    { value: 'highestPackage', label: 'Highest Package' },
    { value: 'establishedYear', label: 'Established Year' },
    { value: 'campusSize', label: 'Campus Size' },
    { value: 'name', label: 'Name (A-Z)' },
  ];

  const courseOptions = [
    'Computer Science',
    'Electronics and Communication',
    'Mechanical Engineering',
    'Civil Engineering',
    'Electrical Engineering',
    'Information Science',
    'Artificial Intelligence',
    'Data Science',
    'Biotechnology',
    'Chemical Engineering',
  ];

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    onSearch(localSearchQuery);
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFilter(newFilters);
  };

  const clearFilters = () => {
    const emptyFilters = {};
    setLocalFilters(emptyFilters);
    onFilter(emptyFilters);
  };

  const hasActiveFilters = Object.keys(localFilters).some(key => 
    localFilters[key] !== undefined && localFilters[key] !== '' && localFilters[key] !== false
  );

  return (
    <div className="bg-white shadow-lg rounded-lg p-6 mb-8">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search colleges by name, acronym, or courses (e.g., 'RVCE', 'Computer Science', 'colleges with >90% placements')"
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900 placeholder-gray-500"
          />
          <button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md transition-colors duration-200"
          >
            Search
          </button>
        </div>
      </form>

      {/* Sort and Filter Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        {/* Sort Dropdown */}
        <div className="flex items-center space-x-2">
          <label htmlFor="sort" className="text-sm font-medium text-gray-700">
            Sort by:
          </label>
          <select
            id="sort"
            value={sortBy}
            onChange={(e) => onSort(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Filter Toggle */}
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="text-sm text-red-600 hover:text-red-700 flex items-center space-x-1"
            >
              <X className="h-4 w-4" />
              <span>Clear Filters</span>
            </button>
          )}
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md transition-colors duration-200"
          >
            <SlidersHorizontal className="h-4 w-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                {Object.keys(localFilters).filter(key => 
                  localFilters[key] !== undefined && localFilters[key] !== '' && localFilters[key] !== false
                ).length}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      {isFilterOpen && (
        <div className="border-t border-gray-200 pt-6 animate-fade-in">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Placement Rate Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Placement Rate
              </label>
              <select
                value={localFilters.minPlacementRate || ''}
                onChange={(e) => handleFilterChange('minPlacementRate', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Any</option>
                <option value="70">70%+</option>
                <option value="80">80%+</option>
                <option value="90">90%+</option>
                <option value="95">95%+</option>
              </select>
            </div>

            {/* Package Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Highest Package
              </label>
              <select
                value={localFilters.minPackage || ''}
                onChange={(e) => handleFilterChange('minPackage', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Any</option>
                <option value="20">₹20 LPA+</option>
                <option value="30">₹30 LPA+</option>
                <option value="40">₹40 LPA+</option>
                <option value="50">₹50 LPA+</option>
              </select>
            </div>

            {/* Metro Access Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Metro Accessibility
              </label>
              <select
                value={localFilters.metroAccess === undefined ? '' : localFilters.metroAccess.toString()}
                onChange={(e) => handleFilterChange('metroAccess', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Any</option>
                <option value="true">Metro Accessible</option>
                <option value="false">Not Metro Accessible</option>
              </select>
            </div>

            {/* Establishment Year Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Established After
              </label>
              <select
                value={localFilters.establishedAfter || ''}
                onChange={(e) => handleFilterChange('establishedAfter', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Any Year</option>
                <option value="2000">After 2000</option>
                <option value="1990">After 1990</option>
                <option value="1980">After 1980</option>
                <option value="1970">After 1970</option>
              </select>
            </div>

            {/* Campus Size Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Campus Size
              </label>
              <select
                value={localFilters.minCampusSize || ''}
                onChange={(e) => handleFilterChange('minCampusSize', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Any Size</option>
                <option value="10">10+ acres</option>
                <option value="20">20+ acres</option>
                <option value="30">30+ acres</option>
                <option value="50">50+ acres</option>
              </select>
            </div>

            {/* Course Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Course/Branch
              </label>
              <select
                value={localFilters.course || ''}
                onChange={(e) => handleFilterChange('course', e.target.value || undefined)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">All Courses</option>
                {courseOptions.map((course) => (
                  <option key={course} value={course}>
                    {course}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
