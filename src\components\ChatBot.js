'use client';

import { useState, useRef, useEffect } from 'react';
import { X, Send, MessageCircle, User, Bot, ExternalLink } from 'lucide-react';
import { extractIntent, extractEntities, searchColleges, generateResponse } from '../lib/chatbotUtils';
import { db } from '../lib/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { v4 as uuidv4 } from 'uuid';

const ChatBot = ({ isOpen, onClose, initialMessage = null }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi! I'm your AI college advisor. I can help you find the perfect engineering college in Bangalore. Ask me about:\n\n• Colleges offering specific courses (AI/ML, CSE, etc.)\n• Placement statistics and packages\n• College comparisons\n• Campus facilities and locations\n\nWhat would you like to know?",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId] = useState(() => uuidv4());
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle initial message if provided
  useEffect(() => {
    if (initialMessage && isOpen) {
      handleSendMessage(initialMessage);
    }
  }, [initialMessage, isOpen]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const saveChatToFirebase = async (userMessage, botResponse) => {
    try {
      await addDoc(collection(db, 'chatSessions'), {
        sessionId,
        userMessage,
        botResponse: botResponse.text,
        timestamp: serverTimestamp(),
        intent: extractIntent(userMessage),
        entities: extractEntities(userMessage)
      });
    } catch (error) {
      console.error('Error saving chat to Firebase:', error);
    }
  };

  const handleSendMessage = async (messageText = null) => {
    const message = messageText || inputMessage.trim();
    if (!message) return;

    // Add user message
    const userMessage = {
      id: Date.now(),
      text: message,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    try {
      // Process the message
      const intent = extractIntent(message);
      const entities = extractEntities(message);
      const colleges = await searchColleges(intent, entities, message);
      const response = generateResponse(intent, colleges, message);

      // Simulate typing delay
      setTimeout(() => {
        const botMessage = {
          id: Date.now() + 1,
          text: response.text,
          sender: 'bot',
          timestamp: new Date(),
          colleges: response.colleges,
          showContactForm: response.showContactForm
        };

        setMessages(prev => [...prev, botMessage]);
        setIsTyping(false);

        // Save to Firebase
        saveChatToFirebase(message, response);
      }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds

    } catch (error) {
      console.error('Error processing message:', error);
      
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm having trouble processing your request right now. Let me connect you with our education counselors who can help you personally!",
        sender: 'bot',
        timestamp: new Date(),
        showContactForm: true
      };

      setMessages(prev => [...prev, errorMessage]);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleContactFormClick = () => {
    // Redirect to contact form or WhatsApp
    window.open('https://wa.me/************?text=Hi! I need personalized guidance for engineering colleges in Bangalore.', '_blank');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-end p-4 pointer-events-none">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md h-[600px] flex flex-col pointer-events-auto border border-gray-200">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-lg flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Bot className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold">AI College Advisor</h3>
              <p className="text-xs text-blue-100">Online • Ready to help</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-800 shadow-sm border'
                }`}
              >
                <div className="flex items-start space-x-2">
                  {message.sender === 'bot' && (
                    <Bot className="w-4 h-4 mt-1 text-blue-600 flex-shrink-0" />
                  )}
                  {message.sender === 'user' && (
                    <User className="w-4 h-4 mt-1 text-white flex-shrink-0" />
                  )}
                  <div className="flex-1">
                    <p className="text-sm whitespace-pre-line">{message.text}</p>
                    
                    {/* Show contact form button if needed */}
                    {message.showContactForm && (
                      <button
                        onClick={handleContactFormClick}
                        className="mt-3 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center space-x-2 transition-colors"
                      >
                        <ExternalLink className="w-4 h-4" />
                        <span>Get Personal Guidance</span>
                      </button>
                    )}
                    
                    {/* Show college cards if available */}
                    {message.colleges && message.colleges.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {message.colleges.slice(0, 2).map((college) => (
                          <div key={college.id} className="bg-gray-50 p-3 rounded-lg border">
                            <h4 className="font-semibold text-sm text-gray-900">{college.name}</h4>
                            <p className="text-xs text-gray-600 mt-1">
                              Ranking: #{college.ranking} • Placement: {college.placementRate}% • Package: ₹{college.highestPackage} LPA
                            </p>
                          </div>
                        ))}
                        {message.colleges.length > 2 && (
                          <p className="text-xs text-gray-500 mt-2">
                            +{message.colleges.length - 2} more colleges available
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-xs opacity-70 mt-2">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))}
          
          {/* Typing indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-white text-gray-800 rounded-lg p-3 shadow-sm border max-w-[80%]">
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4 text-blue-600" />
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 border-t bg-white rounded-b-lg">
          <div className="flex space-x-2">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask about colleges, courses, placements..."
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isTyping}
            />
            <button
              onClick={() => handleSendMessage()}
              disabled={!inputMessage.trim() || isTyping}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2 text-center">
            Powered by AI • For personalized guidance, contact our counselors
          </p>
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
