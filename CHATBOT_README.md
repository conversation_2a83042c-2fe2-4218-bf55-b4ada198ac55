# AI Chatbot Integration for College Website

## Overview
This implementation adds a comprehensive AI chatbot system to your college comparison website with the following features:

### ✅ Implemented Features

#### 1. **Exit-Intent Popup**
- Triggers when user attempts to leave the page
- Message: "Still Confused? Ask Me Anything!"
- Provides options to start chat or WhatsApp consultation
- Only shows once per session

#### 2. **Persistent Chat Button**
- Located on bottom-left of every page
- Animated with pulse effect and notification badge
- Tooltip: "Chat with Our AI Advisor"
- Transforms to close button when chat is open

#### 3. **AI Chatbot Interface**
- Clean, responsive design matching website aesthetics
- Real-time typing indicators
- Message timestamps
- College cards embedded in responses
- Contact form integration for fallback scenarios

#### 4. **Natural Language Processing**
- **Course-specific searches**: "Show me colleges offering AI/ML courses"
- **Placement queries**: "Which college has the highest placement package?"
- **College comparisons**: "Compare RVCE vs PESU for CSE placements"
- **Location-based searches**: "Show me engineering colleges in Bangalore"
- **Fee-related queries**: "Which colleges have fees under 5 lakhs?"

#### 5. **Synonym Recognition**
- "best college" = highest ranking/placement
- "top university" = highest rated
- CSE = Computer Science Engineering
- AI/ML = Artificial Intelligence/Machine Learning

#### 6. **Firebase Integration**
- Chat conversation history storage
- Analytics tracking for popular queries
- Session management
- Real-time data synchronization

## File Structure

```
src/
├── components/
│   ├── ChatBot.js              # Main chatbot interface
│   ├── ChatButton.js           # Persistent chat button
│   ├── ExitIntentPopup.js      # Exit-intent popup
│   └── ChatbotProvider.js      # Main integration component
├── hooks/
│   └── useExitIntent.js        # Exit-intent detection hook
├── lib/
│   ├── chatbotUtils.js         # NLP and query processing
│   └── firebase.js             # Firebase configuration
└── styles/
    └── globals.css             # Additional chatbot styles
```

## Setup Instructions

### 1. Install Dependencies
```bash
npm install firebase uuid
```

### 2. Firebase Setup
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Enable Analytics (optional)
4. Copy your Firebase config
5. Create `.env.local` file:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-ABCDEF
```

### 3. Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /chatSessions/{document} {
      allow read, write: if true;
    }
  }
}
```

## Chatbot Capabilities

### Intent Recognition
The chatbot recognizes these query types:

1. **COURSE_SEARCH**: Course-specific college searches
2. **PLACEMENT_QUERY**: Placement statistics and packages
3. **COLLEGE_COMPARISON**: Side-by-side college comparisons
4. **LOCATION_SEARCH**: Location-based searches
5. **FEE_QUERY**: Fee-related inquiries
6. **GENERAL_INFO**: General information requests

### Entity Extraction
- **Colleges**: RVCE, PESU, BMSCE, MSRIT, etc.
- **Courses**: CSE, AI/ML, ECE, Mechanical, etc.
- **Numbers**: For fees, packages, percentages

### Response Generation
- Contextual responses based on intent
- College cards with key information
- Fallback to contact form when confidence is low
- Personalized recommendations

## Fallback Strategy

When the chatbot cannot provide satisfactory answers:
1. Shows message: "Let me connect you with our education counselors..."
2. Displays "Get Personal Guidance" button
3. Redirects to WhatsApp consultation
4. Pre-populates contact form with user's query

## Analytics & Tracking

The system tracks:
- Chat engagement rate
- Query types and frequency
- Response satisfaction
- Contact form conversion rate
- Popular college searches

## Customization Options

### 1. Modify Intents
Edit `src/lib/chatbotUtils.js` to add new intent patterns:

```javascript
const INTENT_PATTERNS = {
  NEW_INTENT: [
    /pattern1/i,
    /pattern2/i
  ]
};
```

### 2. Update College Data
The chatbot uses your existing `college.json` data automatically.

### 3. Customize Responses
Modify the `generateResponse` function in `chatbotUtils.js`.

### 4. Styling
Update CSS classes in component files or `globals.css`.

## Performance Considerations

- Lazy loading of Firebase only when needed
- Efficient college data filtering
- Debounced typing indicators
- Session-based exit intent (shows only once)
- Optimized re-renders with React hooks

## Testing Scenarios

Test these queries to verify functionality:

1. "Show me colleges offering AI/ML courses"
2. "Which college has the highest placement package?"
3. "Compare RVCE vs PESU"
4. "Engineering colleges in Bangalore"
5. "Colleges with 90% placement rate"
6. "Tell me about MSRIT"

## Future Enhancements

1. **Advanced NLP**: Integration with OpenAI or Google Dialogflow
2. **Voice Chat**: Speech-to-text and text-to-speech
3. **Multi-language**: Support for regional languages
4. **Personalization**: User preference learning
5. **Integration**: CRM and email automation
6. **Analytics Dashboard**: Admin panel for insights

## Troubleshooting

### Common Issues:

1. **Firebase not connecting**: Check environment variables
2. **Exit intent not working**: Ensure proper mouse event handling
3. **Chat not opening**: Check for JavaScript errors in console
4. **Styling issues**: Verify Tailwind CSS classes

### Debug Mode:
Add `console.log` statements in `chatbotUtils.js` to debug intent recognition and entity extraction.

## Support

For technical support or customization requests, contact the development team.
