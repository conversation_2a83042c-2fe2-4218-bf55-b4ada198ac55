'use client';

import { useState, useEffect } from 'react';
import { MessageCircle, X } from 'lucide-react';

const ChatButton = ({ onClick, isOpen }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showPulse, setShowPulse] = useState(true);

  useEffect(() => {
    // Show the button after a short delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Stop pulsing after user interacts
    if (isOpen) {
      setShowPulse(false);
    }
  }, [isOpen]);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 left-6 z-40">
      {/* Tooltip */}
      {!isOpen && (
        <div className="absolute bottom-full left-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-gray-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap">
            Chat with Our AI Advisor
            <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      )}

      {/* Chat Button */}
      <button
        onClick={onClick}
        className={`group relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 ${
          showPulse && !isOpen ? 'animate-pulse' : ''
        }`}
        aria-label="Chat with Our AI Advisor"
      >
        {/* Pulse rings */}
        {showPulse && !isOpen && (
          <>
            <div className="absolute inset-0 rounded-full bg-blue-400 opacity-75 animate-ping"></div>
            <div className="absolute inset-0 rounded-full bg-blue-400 opacity-50 animate-ping" style={{ animationDelay: '0.5s' }}></div>
          </>
        )}
        
        {/* Icon */}
        <div className="relative z-10">
          {isOpen ? (
            <X className="w-6 h-6" />
          ) : (
            <MessageCircle className="w-6 h-6" />
          )}
        </div>

        {/* Notification badge */}
        {!isOpen && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
            <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
          </div>
        )}
      </button>

      {/* Call-to-action text */}
      {!isOpen && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
          <div className="bg-white text-gray-800 text-sm font-medium px-4 py-2 rounded-lg shadow-lg border whitespace-nowrap">
            💬 Need help choosing a college?
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatButton;
